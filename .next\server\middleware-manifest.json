{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_452e0654.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "ef81d7846f8daa3acb8f32de0f6ddb2a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "976dbbe33fe7002864d13679116b530390364a2a832a4ea11059a28bafa6df55", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e0f5e3d6ef493c450b9d3db420f3d5171624b61eccf386a69c3cd564704d4103"}}}, "sortedMiddleware": ["/"], "functions": {}}